"use client"

import { useEffect, useRef, useState } from "react"
import { useSound } from "./sound-provider"

interface NotificationOptions {
  title: string
  body: string
  icon?: string
  silent?: boolean
  tag?: string
  requireInteraction?: boolean
  actions?: NotificationAction[]
}

interface RobustNotificationManagerProps {
  onPermissionChange?: (permission: NotificationPermission) => void
}

export function RobustNotificationManager({ onPermissionChange }: RobustNotificationManagerProps) {
  const [permission, setPermission] = useState<NotificationPermission>("default")
  const [isSupported, setIsSupported] = useState(false)
  const { playSound } = useSound()
  const notificationRef = useRef<Notification | null>(null)

  // Vérifier le support des notifications
  useEffect(() => {
    if (typeof window !== "undefined" && "Notification" in window) {
      setIsSupported(true)
      setPermission(Notification.permission)
      onPermissionChange?.(Notification.permission)
      console.log("Notifications API is supported, current permission:", Notification.permission)
    } else {
      setIsSupported(false)
      console.warn("Notifications API is not supported in this browser")
    }
  }, [onPermissionChange])

  // Demander la permission pour les notifications
  const requestPermission = async (): Promise<NotificationPermission> => {
    if (!isSupported) {
      console.warn("Notifications not supported")
      return "denied"
    }

    if (permission === "granted") {
      return "granted"
    }

    try {
      const result = await Notification.requestPermission()
      setPermission(result)
      onPermissionChange?.(result)
      console.log("Notification permission result:", result)
      return result
    } catch (error) {
      console.error("Error requesting notification permission:", error)
      return "denied"
    }
  }

  // Afficher une notification robuste avec son
  const showNotification = async (options: NotificationOptions): Promise<boolean> => {
    if (!isSupported) {
      console.warn("Notifications not supported, falling back to sound only")
      await playSound("bell", { priority: "high", repeat: 3 })
      return false
    }

    // Demander la permission si nécessaire
    if (permission !== "granted") {
      const newPermission = await requestPermission()
      if (newPermission !== "granted") {
        console.warn("Notification permission denied, falling back to sound only")
        await playSound("bell", { priority: "high", repeat: 3 })
        return false
      }
    }

    try {
      // Fermer la notification précédente si elle existe
      if (notificationRef.current) {
        notificationRef.current.close()
      }

      // Créer la nouvelle notification
      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon || "/icons/icon-192x192.png",
        silent: options.silent !== false, // Par défaut silencieux car on gère le son séparément
        tag: options.tag || "timer-notification",
        requireInteraction: options.requireInteraction || true,
        actions: options.actions || []
      })

      notificationRef.current = notification

      // Jouer le son en parallèle pour s'assurer qu'il se déclenche
      if (!options.silent) {
        playSound("bell", { priority: "high", repeat: 2 })
      }

      // Gérer les événements de la notification
      notification.onclick = () => {
        window.focus()
        notification.close()
      }

      notification.onclose = () => {
        notificationRef.current = null
      }

      notification.onerror = (error) => {
        console.error("Notification error:", error)
        notificationRef.current = null
      }

      // Auto-fermer après 10 secondes si pas d'interaction requise
      if (!options.requireInteraction) {
        setTimeout(() => {
          if (notificationRef.current === notification) {
            notification.close()
          }
        }, 10000)
      }

      console.log("Notification shown successfully")
      return true

    } catch (error) {
      console.error("Error showing notification:", error)
      // Fallback: jouer le son seulement
      await playSound("bell", { priority: "high", repeat: 3 })
      return false
    }
  }

  // Fermer la notification actuelle
  const closeNotification = () => {
    if (notificationRef.current) {
      notificationRef.current.close()
      notificationRef.current = null
    }
  }

  // Nettoyer au démontage
  useEffect(() => {
    return () => {
      closeNotification()
    }
  }, [])

  return {
    isSupported,
    permission,
    requestPermission,
    showNotification,
    closeNotification
  }
}

// Hook pour utiliser le gestionnaire de notifications
export function useRobustNotifications() {
  const [permission, setPermission] = useState<NotificationPermission>("default")
  const managerRef = useRef<ReturnType<typeof RobustNotificationManager> | null>(null)

  useEffect(() => {
    managerRef.current = RobustNotificationManager({ onPermissionChange: setPermission })
  }, [])

  return {
    permission,
    requestPermission: () => managerRef.current?.requestPermission(),
    showNotification: (options: NotificationOptions) => managerRef.current?.showNotification(options),
    closeNotification: () => managerRef.current?.closeNotification(),
    isSupported: managerRef.current?.isSupported || false
  }
}

// Composant pour gérer les notifications de manière déclarative
export function NotificationProvider({ children }: { children: React.ReactNode }) {
  RobustNotificationManager({})
  return <>{children}</>
}
