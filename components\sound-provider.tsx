"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect, useRef } from "react"

type SoundType = "bell" | "alarm" | "notification"

interface SoundContextType {
  playSound: (type: SoundType, options?: { priority?: 'high' | 'normal', repeat?: number }) => Promise<boolean>
  isSoundEnabled: boolean
  toggleSound: () => void
  requestWakeLock: () => Promise<boolean>
  releaseWakeLock: () => void
  isWakeLockActive: boolean
}

const SoundContext = createContext<SoundContextType | null>(null)

export function SoundProvider({ children }: { children: React.ReactNode }) {
  const [isSoundEnabled, setIsSoundEnabled] = useState(true)
  const [isWakeLockActive, setIsWakeLockActive] = useState(false)
  const audioContextRef = useRef<AudioContext | null>(null)
  const wakeLockRef = useRef<WakeLockSentinel | null>(null)
  const audioBuffersRef = useRef<Map<SoundType, AudioBuffer>>(new Map())
  const pendingSoundsRef = useRef<Set<string>>(new Set())

  // Wake Lock Management
  const requestWakeLock = async (): Promise<boolean> => {
    if (typeof window === "undefined" || !("wakeLock" in navigator)) {
      console.warn("Wake Lock API not supported")
      return false
    }

    try {
      if (wakeLockRef.current) {
        console.log("Wake lock already active")
        return true
      }

      wakeLockRef.current = await navigator.wakeLock.request("screen")
      setIsWakeLockActive(true)
      console.log("Wake lock activated successfully")

      // Handle wake lock release
      wakeLockRef.current.addEventListener("release", () => {
        console.log("Wake lock was released")
        setIsWakeLockActive(false)
        wakeLockRef.current = null
      })

      return true
    } catch (error) {
      console.error("Failed to request wake lock:", error)
      return false
    }
  }

  const releaseWakeLock = () => {
    if (wakeLockRef.current) {
      wakeLockRef.current.release()
      wakeLockRef.current = null
      setIsWakeLockActive(false)
      console.log("Wake lock released manually")
    }
  }

  // Initialiser le contexte audio avec gestion robuste
  const initAudioContext = async (): Promise<boolean> => {
    if (typeof window === "undefined") return false

    try {
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
      if (!AudioContextClass) {
        console.warn("AudioContext not supported in this browser")
        return false
      }

      if (!audioContextRef.current) {
        audioContextRef.current = new AudioContextClass()
        console.log("AudioContext initialized successfully")
      }

      // Assurer que le contexte audio est actif
      if (audioContextRef.current.state === "suspended") {
        await audioContextRef.current.resume()
        console.log("AudioContext resumed from suspended state")
      }

      // Précharger les buffers audio
      await preloadAudioBuffers()

      return true
    } catch (error) {
      console.error("Failed to initialize AudioContext:", error)
      return false
    }
  }

  // Précharger les buffers audio pour une lecture plus rapide
  const preloadAudioBuffers = async () => {
    if (!audioContextRef.current) return

    try {
      // Précharger le fichier bell.mp3
      const response = await fetch("/sound/bell.mp3")
      const arrayBuffer = await response.arrayBuffer()
      const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer)
      audioBuffersRef.current.set("bell", audioBuffer)
      console.log("Bell audio buffer preloaded")
    } catch (error) {
      console.warn("Failed to preload bell audio buffer:", error)
    }
  }

  // Initialiser le contexte audio et gérer la visibilité de la page
  useEffect(() => {
    initAudioContext()

    // Gérer les changements de visibilité de la page
    const handleVisibilityChange = async () => {
      if (!document.hidden && audioContextRef.current) {
        // Page redevient visible, s'assurer que l'audio fonctionne
        if (audioContextRef.current.state === "suspended") {
          try {
            await audioContextRef.current.resume()
            console.log("AudioContext resumed after page became visible")
          } catch (error) {
            console.error("Failed to resume AudioContext:", error)
          }
        }
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)

    // Nettoyer lors du démontage
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
      releaseWakeLock()
      if (audioContextRef.current) {
        try {
          if (audioContextRef.current.state !== "closed") {
            audioContextRef.current.close()
          }
        } catch (error) {
          console.error("Error closing AudioContext:", error)
        }
      }
    }
  }, [])

  // Débloquer l'audio sur interaction utilisateur avec gestion améliorée
  useEffect(() => {
    if (typeof window === "undefined") return

    const unlockAudio = async () => {
      try {
        // Initialiser le contexte audio si ce n'est pas déjà fait
        if (!audioContextRef.current) {
          await initAudioContext()
        }

        // Débloquer le contexte audio
        if (audioContextRef.current && audioContextRef.current.state === "suspended") {
          await audioContextRef.current.resume()
          console.log("AudioContext unlocked by user interaction")
        }

        // Jouer un son silencieux pour débloquer l'audio sur iOS
        if (audioContextRef.current && audioContextRef.current.state === "running") {
          try {
            const oscillator = audioContextRef.current.createOscillator()
            const gainNode = audioContextRef.current.createGain()

            gainNode.gain.value = 0.001 // Presque silencieux
            oscillator.connect(gainNode)
            gainNode.connect(audioContextRef.current.destination)

            oscillator.start(0)
            oscillator.stop(0.001)
            console.log("Silent unlock sound played")
          } catch (e) {
            console.warn("Failed to play silent sound:", e)
          }
        }
      } catch (error) {
        console.error("Failed to unlock audio:", error)
      }
    }

    // Ajouter des écouteurs d'événements pour débloquer l'audio
    const unlockEvents = ["touchstart", "touchend", "mousedown", "keydown", "click"]
    unlockEvents.forEach((event) => {
      document.addEventListener(event, unlockAudio, { once: true })
    })

    // Essayer de débloquer immédiatement (pour les navigateurs de bureau)
    unlockAudio()

    return () => {
      unlockEvents.forEach((event) => {
        document.removeEventListener(event, unlockAudio)
      })
    }
  }, [])

  // Fonction pour générer un son de type "bell"
  const generateBellSound = () => {
    if (!audioContextRef.current) return

    try {
      const ctx = audioContextRef.current
      const now = ctx.currentTime

      // Créer les nœuds audio
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()

      // Configurer l'oscillateur
      oscillator.type = "sine"
      oscillator.frequency.value = 600

      // Configurer l'enveloppe de volume
      gainNode.gain.setValueAtTime(0, now)
      gainNode.gain.linearRampToValueAtTime(0.3, now + 0.02)
      gainNode.gain.linearRampToValueAtTime(0.2, now + 0.1)
      gainNode.gain.linearRampToValueAtTime(0, now + 0.3)

      // Connecter les nœuds
      oscillator.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Jouer le son
      oscillator.start(now)
      oscillator.stop(now + 0.3)

      // Nettoyer les ressources
      setTimeout(() => {
        oscillator.disconnect()
        gainNode.disconnect()
      }, 300)

      console.log("Bell sound played")
    } catch (error) {
      console.error("Error generating bell sound:", error)
    }
  }

  // Fonction pour générer un son de type "alarm"
  const generateAlarmSound = () => {
    if (!audioContextRef.current) return

    try {
      const ctx = audioContextRef.current
      const now = ctx.currentTime

      // Créer les nœuds audio
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()

      // Configurer l'oscillateur
      oscillator.type = "sawtooth"
      oscillator.frequency.value = 800

      // Créer un effet de pulsation pour l'alarme
      gainNode.gain.setValueAtTime(0, now)
      gainNode.gain.linearRampToValueAtTime(0.4, now + 0.05)
      gainNode.gain.linearRampToValueAtTime(0.1, now + 0.25)
      gainNode.gain.linearRampToValueAtTime(0.4, now + 0.5)
      gainNode.gain.linearRampToValueAtTime(0, now + 0.6)

      // Connecter les nœuds
      oscillator.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Jouer le son
      oscillator.start(now)
      oscillator.stop(now + 0.6)

      // Nettoyer les ressources
      setTimeout(() => {
        oscillator.disconnect()
        gainNode.disconnect()
      }, 600)

      console.log("Alarm sound played")
    } catch (error) {
      console.error("Error generating alarm sound:", error)
    }
  }

  // Fonction pour générer un son de type "notification"
  const generateNotificationSound = () => {
    if (!audioContextRef.current) return

    try {
      const ctx = audioContextRef.current
      const now = ctx.currentTime

      // Créer les nœuds audio
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()

      // Configurer l'oscillateur avec un glissement de fréquence
      oscillator.type = "sine"
      oscillator.frequency.setValueAtTime(1200, now)
      oscillator.frequency.exponentialRampToValueAtTime(500, now + 0.2)

      // Configurer l'enveloppe de volume
      gainNode.gain.setValueAtTime(0, now)
      gainNode.gain.linearRampToValueAtTime(0.2, now + 0.02)
      gainNode.gain.linearRampToValueAtTime(0.2, now + 0.1)
      gainNode.gain.linearRampToValueAtTime(0, now + 0.2)

      // Connecter les nœuds
      oscillator.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Jouer le son
      oscillator.start(now)
      oscillator.stop(now + 0.2)

      // Nettoyer les ressources
      setTimeout(() => {
        oscillator.disconnect()
        gainNode.disconnect()
      }, 200)

      console.log("Notification sound played")
    } catch (error) {
      console.error("Error generating notification sound:", error)
    }
  }

  // Fonction robuste pour jouer un son avec fallbacks multiples
  const playSound = async (type: SoundType, options?: { priority?: 'high' | 'normal', repeat?: number }): Promise<boolean> => {
    if (!isSoundEnabled) {
      console.log("Sound is disabled")
      return false
    }

    const priority = options?.priority || 'normal'
    const repeat = options?.repeat || 1
    const soundId = `${type}-${Date.now()}-${Math.random()}`

    // Éviter les sons en double
    if (pendingSoundsRef.current.has(type)) {
      console.log(`Sound ${type} already playing, skipping`)
      return false
    }

    pendingSoundsRef.current.add(type)
    console.log(`Playing ${type} sound with priority ${priority}, repeat ${repeat}`)

    try {
      // S'assurer que l'AudioContext est prêt
      if (!audioContextRef.current || audioContextRef.current.state !== "running") {
        await initAudioContext()
      }

      let success = false

      // Méthode 1: Utiliser le buffer préchargé (plus rapide)
      if (type === "bell" && audioBuffersRef.current.has("bell")) {
        success = await playBufferedSound("bell", repeat)
        if (success) {
          pendingSoundsRef.current.delete(type)
          return true
        }
      }

      // Méthode 2: Utiliser l'élément Audio HTML5
      if (type === "bell") {
        success = await playAudioElement(type, repeat)
        if (success) {
          pendingSoundsRef.current.delete(type)
          return true
        }
      }

      // Méthode 3: Utiliser les sons synthétiques (fallback)
      success = await playSyntheticSound(type, repeat)
      pendingSoundsRef.current.delete(type)
      return success

    } catch (error) {
      console.error(`Error playing ${type} sound:`, error)
      pendingSoundsRef.current.delete(type)

      // Dernier recours: notification système si priorité haute
      if (priority === 'high' && 'Notification' in window && Notification.permission === 'granted') {
        try {
          new Notification("Timer Finished", {
            body: "Your timer has completed",
            icon: "/icons/icon-192x192.png",
            silent: false
          })
          return true
        } catch (notifError) {
          console.error("Failed to show notification:", notifError)
        }
      }

      return false
    }
  }

  // Jouer un son depuis un buffer préchargé
  const playBufferedSound = async (type: SoundType, repeat: number = 1): Promise<boolean> => {
    if (!audioContextRef.current || !audioBuffersRef.current.has(type)) return false

    try {
      for (let i = 0; i < repeat; i++) {
        const source = audioContextRef.current.createBufferSource()
        const gainNode = audioContextRef.current.createGain()

        source.buffer = audioBuffersRef.current.get(type)!
        gainNode.gain.value = 0.7

        source.connect(gainNode)
        gainNode.connect(audioContextRef.current.destination)

        source.start(0)

        if (i < repeat - 1) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      }

      console.log(`Buffered ${type} sound played ${repeat} times`)
      return true
    } catch (error) {
      console.error("Error playing buffered sound:", error)
      return false
    }
  }

  // Jouer un son avec l'élément Audio HTML5
  const playAudioElement = async (type: SoundType, repeat: number = 1): Promise<boolean> => {
    if (type !== "bell") return false

    try {
      for (let i = 0; i < repeat; i++) {
        const audio = new Audio("/sound/bell.mp3")
        audio.volume = 0.7

        await new Promise((resolve, reject) => {
          audio.onended = resolve
          audio.onerror = reject
          audio.play().catch(reject)
        })

        if (i < repeat - 1) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      }

      console.log(`Audio element ${type} sound played ${repeat} times`)
      return true
    } catch (error) {
      console.error("Error playing audio element:", error)
      return false
    }
  }

  // Jouer un son synthétique
  const playSyntheticSound = async (type: SoundType, repeat: number = 1): Promise<boolean> => {
    if (!audioContextRef.current) return false

    try {
      for (let i = 0; i < repeat; i++) {
        switch (type) {
          case "bell":
            generateBellSound()
            break
          case "alarm":
            generateAlarmSound()
            break
          case "notification":
            generateNotificationSound()
            break
        }

        if (i < repeat - 1) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      }

      console.log(`Synthetic ${type} sound played ${repeat} times`)
      return true
    } catch (error) {
      console.error("Error playing synthetic sound:", error)
      return false
    }
  }

  const toggleSound = () => {
    setIsSoundEnabled(!isSoundEnabled)
  }

  return (
    <SoundContext.Provider
      value={{
        playSound,
        isSoundEnabled,
        toggleSound,
        requestWakeLock,
        releaseWakeLock,
        isWakeLockActive
      }}
    >
      {children}
    </SoundContext.Provider>
  )
}

export function useSound() {
  const context = useContext(SoundContext)
  if (!context) {
    throw new Error("useSound must be used within a SoundProvider")
  }
  return context
}

