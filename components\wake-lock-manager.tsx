"use client"

import { useEffect, useRef, useState } from "react"
import { useSound } from "./sound-provider"

interface WakeLockManagerProps {
  isActive: boolean
  onWakeLockChange?: (isActive: boolean) => void
}

export function WakeLockManager({ isActive, onWakeLockChange }: WakeLockManagerProps) {
  const [wakeLockSupported, setWakeLockSupported] = useState(false)
  const { requestWakeLock, releaseWakeLock, isWakeLockActive } = useSound()
  const previousActiveRef = useRef(isActive)

  // Vérifier le support du Wake Lock
  useEffect(() => {
    if (typeof window !== "undefined" && "wakeLock" in navigator) {
      setWakeLockSupported(true)
      console.log("Wake Lock API is supported")
    } else {
      setWakeLockSupported(false)
      console.warn("Wake Lock API is not supported in this browser")
    }
  }, [])

  // Gérer l'activation/désactivation du Wake Lock
  useEffect(() => {
    if (!wakeLockSupported) return

    const handleWakeLock = async () => {
      if (isActive && !previousActiveRef.current) {
        // Activer le Wake Lock
        console.log("Requesting wake lock for timer")
        const success = await requestWakeLock()
        onWakeLockChange?.(success)
      } else if (!isActive && previousActiveRef.current) {
        // Désactiver le Wake Lock
        console.log("Releasing wake lock for timer")
        releaseWakeLock()
        onWakeLockChange?.(false)
      }
      
      previousActiveRef.current = isActive
    }

    handleWakeLock()
  }, [isActive, wakeLockSupported, requestWakeLock, releaseWakeLock, onWakeLockChange])

  // Gérer la visibilité de la page
  useEffect(() => {
    if (!wakeLockSupported) return

    const handleVisibilityChange = async () => {
      if (document.hidden) {
        console.log("Page hidden, wake lock may be released by browser")
      } else if (isActive && !isWakeLockActive) {
        // Page redevient visible et le timer est actif, réactiver le Wake Lock
        console.log("Page visible again, re-requesting wake lock")
        const success = await requestWakeLock()
        onWakeLockChange?.(success)
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)
    
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [isActive, isWakeLockActive, wakeLockSupported, requestWakeLock, onWakeLockChange])

  // Nettoyer le Wake Lock au démontage
  useEffect(() => {
    return () => {
      if (isWakeLockActive) {
        releaseWakeLock()
      }
    }
  }, [isWakeLockActive, releaseWakeLock])

  // Ce composant ne rend rien
  return null
}

// Hook pour utiliser le Wake Lock Manager facilement
export function useWakeLockManager(isActive: boolean) {
  const [wakeLockActive, setWakeLockActive] = useState(false)
  
  return {
    wakeLockActive,
    WakeLockComponent: () => (
      <WakeLockManager 
        isActive={isActive} 
        onWakeLockChange={setWakeLockActive}
      />
    )
  }
}
