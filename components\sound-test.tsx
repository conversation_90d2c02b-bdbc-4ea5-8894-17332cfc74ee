"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useSound } from "@/components/sound-provider"
import { Volume2, VolumeX, Smartphone, Lock, Unlock } from "lucide-react"

export function SoundTest() {
  const { playSound, isSoundEnabled, toggleSound, requestWakeLock, releaseWakeLock, isWakeLockActive } = useSound()
  const [testResults, setTestResults] = useState<string[]>([])
  const [isTestingBackground, setIsTestingBackground] = useState(false)

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  const testBasicSound = async () => {
    addResult("Testing basic sound...")
    try {
      const success = await playSound("bell")
      addResult(`Basic sound test: ${success ? "SUCCESS" : "FAILED"}`)
    } catch (error) {
      addResult(`Basic sound test: ERROR - ${error}`)
    }
  }

  const testHighPrioritySound = async () => {
    addResult("Testing high priority sound with repeat...")
    try {
      const success = await playSound("bell", { priority: "high", repeat: 3 })
      addResult(`High priority sound test: ${success ? "SUCCESS" : "FAILED"}`)
    } catch (error) {
      addResult(`High priority sound test: ERROR - ${error}`)
    }
  }

  const testWakeLock = async () => {
    addResult("Testing Wake Lock...")
    try {
      if (isWakeLockActive) {
        releaseWakeLock()
        addResult("Wake Lock released")
      } else {
        const success = await requestWakeLock()
        addResult(`Wake Lock request: ${success ? "SUCCESS" : "FAILED"}`)
      }
    } catch (error) {
      addResult(`Wake Lock test: ERROR - ${error}`)
    }
  }

  const testBackgroundSound = async () => {
    setIsTestingBackground(true)
    addResult("Starting background sound test...")
    addResult("Please minimize the browser or lock your screen now...")
    
    // Attendre 5 secondes puis jouer le son
    setTimeout(async () => {
      addResult("Playing sound in background...")
      try {
        const success = await playSound("bell", { priority: "high", repeat: 2 })
        addResult(`Background sound test: ${success ? "SUCCESS" : "FAILED"}`)
      } catch (error) {
        addResult(`Background sound test: ERROR - ${error}`)
      }
      setIsTestingBackground(false)
    }, 5000)
  }

  const testServiceWorkerSound = () => {
    addResult("Testing Service Worker sound...")
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'PLAY_SOUND',
        soundType: 'bell',
        priority: 'high',
        repeat: 2
      })
      addResult("Service Worker sound message sent")
    } else {
      addResult("Service Worker not available")
    }
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Volume2 className="h-5 w-5" />
          Test du Système de Son Robuste
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status */}
        <div className="flex flex-wrap gap-2">
          <Badge variant={isSoundEnabled ? "default" : "secondary"}>
            {isSoundEnabled ? "Son Activé" : "Son Désactivé"}
          </Badge>
          <Badge variant={isWakeLockActive ? "default" : "secondary"}>
            {isWakeLockActive ? "Wake Lock Actif" : "Wake Lock Inactif"}
          </Badge>
          <Badge variant="outline">
            {document.hidden ? "Page Cachée" : "Page Visible"}
          </Badge>
        </div>

        {/* Controls */}
        <div className="flex flex-wrap gap-2">
          <Button onClick={toggleSound} variant="outline" size="sm">
            {isSoundEnabled ? <VolumeX className="h-4 w-4 mr-2" /> : <Volume2 className="h-4 w-4 mr-2" />}
            {isSoundEnabled ? "Désactiver Son" : "Activer Son"}
          </Button>
          
          <Button onClick={testWakeLock} variant="outline" size="sm">
            {isWakeLockActive ? <Unlock className="h-4 w-4 mr-2" /> : <Lock className="h-4 w-4 mr-2" />}
            {isWakeLockActive ? "Libérer Wake Lock" : "Activer Wake Lock"}
          </Button>
        </div>

        {/* Test Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <Button onClick={testBasicSound} disabled={!isSoundEnabled}>
            Test Son Basique
          </Button>
          
          <Button onClick={testHighPrioritySound} disabled={!isSoundEnabled}>
            Test Son Priorité Haute
          </Button>
          
          <Button 
            onClick={testBackgroundSound} 
            disabled={!isSoundEnabled || isTestingBackground}
            className="col-span-2"
          >
            <Smartphone className="h-4 w-4 mr-2" />
            {isTestingBackground ? "Test en cours..." : "Test Son en Arrière-plan"}
          </Button>
          
          <Button onClick={testServiceWorkerSound} disabled={!isSoundEnabled}>
            Test Service Worker
          </Button>
          
          <Button onClick={clearResults} variant="outline">
            Effacer Résultats
          </Button>
        </div>

        {/* Results */}
        {testResults.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-medium">Résultats des Tests:</h3>
            <div className="bg-muted p-3 rounded-md max-h-60 overflow-y-auto">
              {testResults.map((result, index) => (
                <div key={index} className="text-sm font-mono">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="text-sm text-muted-foreground space-y-2">
          <p><strong>Instructions pour tester:</strong></p>
          <ul className="list-disc list-inside space-y-1">
            <li>Activez le Wake Lock avant de tester</li>
            <li>Pour le test en arrière-plan, minimisez la fenêtre ou verrouillez l'écran après avoir cliqué</li>
            <li>Vérifiez que les sons se déclenchent même quand l'écran est verrouillé</li>
            <li>Le Service Worker devrait fonctionner même si la page est fermée</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
